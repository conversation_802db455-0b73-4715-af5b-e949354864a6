package notice

import (
	"fmt"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
	"marketing/internal/service/system"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type PushHandler interface {
	Index(c *gin.Context)
	Create(c *gin.Context)
	Store(c *gin.Context)
	Users(c *gin.Context)
	Revoke(c *gin.Context)
	GetNotifyDetail(c *gin.Context)
}

type pushHandler struct {
	appNotificationTypeSvc service.AppNotificationTypeSvc
	pushSvc                service.PushNotificationSvc
	appSvc                 system.AppSystemSvc
	trainSvc               service.TrainService
}

func NewPushHandler(
	appNotificationTypeSvc service.AppNotificationTypeSvc,
	pushSvc service.PushNotificationSvc,
	appSvc system.AppSystemSvc,
	trainSvc service.TrainService,
) PushHandler {
	return &pushHandler{
		appNotificationTypeSvc: appNotificationTypeSvc,
		pushSvc:                pushSvc,
		appSvc:                 appSvc,
		trainSvc:               trainSvc,
	}
}

// Index 推送消息列表页面
func (h *pushHandler) Index(c *gin.Context) {
	// 获取分页参数
	pageNum := e.ReqParamInt(c, "page", 1)
	pageSize := e.ReqParamInt(c, "page_size", 20)

	// 获取筛选参数
	typeID := e.ReqParamInt(c, "type_id", 0)
	content := e.ReqParamStr(c, "content")

	// 获取推送消息列表
	list, total, err := h.pushSvc.GetPushNotificationList(c, pageNum, pageSize, typeID, content)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"data":      list,
		"total":     total,
		"page":      pageNum,
		"page_size": pageSize,
	})
}

// Create 创建推送消息页面
func (h *pushHandler) Create(c *gin.Context) {
	// 获取可手动推送的通知类型
	notificationTypes, err := h.appNotificationTypeSvc.GetManualNotificationTypes(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 获取企微标签
	notificationTags, err := h.pushSvc.GetNotificationTags(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 获取角色列表
	roles, err := h.pushSvc.GetActiveRoles(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 获取最新的培训资源
	newestTrains, err := h.pushSvc.GetNewestTrains(c, 50)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 获取最新的终端通知
	newestNotices, err := h.pushSvc.GetNewestNotices(c, 5)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"notification_types": notificationTypes,
		"notification_tags":  notificationTags,
		"roles":              roles,
		"newest_trains":      newestTrains,
		"newest_notices":     newestNotices,
	})
}

// Store 存储推送消息
func (h *pushHandler) Store(c *gin.Context) {
	var req StorePushNotificationReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// 获取通知类型信息
	notificationType, err := h.appNotificationTypeSvc.GetAppNotificationTypeBySlug(c, req.Slug)
	if err != nil {
		handler.Error(c, err)
		return
	}
	if notificationType == nil {
		handler.Error(c, errors.NewErr("通知类型不存在"))
		return
	}

	// 处理推送目标
	audience, err := h.processAudience(req.Audience)
	if err != nil {
		handler.Error(c, err)
		return
	}

	url := req.URL
	if url == "" {
		url = notificationType.URL
	}

	// 如果是通知类型，添加ID参数
	var noticeID string
	if req.Slug == "notice" || req.Slug == "train_resource_update" {
		if contentMap, ok := req.Content.(map[string]interface{}); ok {
			if id, exists := contentMap["id"]; exists {
				url = "" //用通用url 那id应该是通知id
				noticeID = cast.ToString(id)
			}
		}
	}

	// 格式化消息内容
	content := h.formatMessageContent(c, req.Slug, req.Content, true)
	title := h.getMessageTitle(req.Slug, req.Content)

	// 推送消息
	err = h.pushSvc.PushNotification(c, &service.PushNotificationParams{
		Title:    title,
		Content:  content,
		Audience: audience,
		Slug:     req.Slug,
		Platform: req.Platform,
		URL:      url,
		NoticeID: noticeID,
	})

	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"success": true,
	})
}

// Users 搜索用户
func (h *pushHandler) Users(c *gin.Context) {
	page := c.DefaultQuery("page", "1")
	search := c.Query("search")

	if search == "" {
		handler.Success(c, []interface{}{})
		return
	}

	pageNum, err := strconv.Atoi(page)
	if err != nil {
		pageNum = 1
	}

	users, err := h.pushSvc.SearchUsers(c, search, pageNum, 10)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, users)
}

// Revoke 撤回推送消息
func (h *pushHandler) Revoke(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	err = h.pushSvc.RevokeNotification(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"success": true,
	})
}

// GetNotifyDetail 消息详情
func (h *pushHandler) GetNotifyDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}
	detail, err := h.pushSvc.GetNotifyDetail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, detail)
}

// processAudience 处理推送目标
func (h *pushHandler) processAudience(audience map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	if tags, ok := audience["tags"].([]interface{}); ok {
		var tagTexts []string
		for _, tag := range tags {
			tagTexts = append(tagTexts, fmt.Sprintf("%v", tag))
		}
		result["tags"] = tagTexts
	} else if users, ok := audience["users"].([]interface{}); ok {
		var userIDs []uint
		for _, user := range users {
			userIDs = append(userIDs, cast.ToUint(user.(float64)))
		}
		result["users"] = userIDs
	} else if roles, ok := audience["roles"].([]interface{}); ok {
		var roleIDs []uint
		for _, role := range roles {
			roleIDs = append(roleIDs, cast.ToUint(role.(float64)))
		}
		result["roles"] = roleIDs
	} else if all, ok := audience["all"].(string); ok && all == "all" {
		// 如果是字符串"all"，直接返回
		if audienceStr, ok := audience["all"].(string); ok && audienceStr == "all" {
			result["all"] = "all"
		}
	} else {
		return result, errors.NewErr("无效的推送目标")
	}

	return result, nil
}

// formatMessageContent 格式化消息内容
func (h *pushHandler) formatMessageContent(c *gin.Context, notificationType string, content interface{}, breakLine bool) string {
	var newContent string

	switch notificationType {
	case "train_resource_update":
		if contentMap, ok := content.(map[string]interface{}); ok {
			var ids []int
			if id, exists := contentMap["id"]; exists {
				ids = append(ids, cast.ToInt(id))
			}
			//这里需要根据ID获取培训资源信息
			trainsTypes, err := h.trainSvc.AllTips(c, "train_type")
			if err != nil {
				return ""
			}

			trains, err := h.trainSvc.GetByIDs(c, ids)
			if err != nil {
				return ""
			}
			for _, train := range trains {

				trainType := ""
				for _, trainTypeItem := range trainsTypes {
					if cast.ToUint(trainTypeItem["id"]) == train.Type {
						trainType = cast.ToString(trainTypeItem["title"])
						break
					}
				}
				newContent += "[" + trainType + "]" + train.Name + "\n"
			}
		}
	case "notice":
		newContent = ""
	default:
		if contentStr, ok := content.(string); ok {
			newContent = contentStr
		}
	}

	if !breakLine {
		newContent = strings.ReplaceAll(newContent, "\n", "")
	}

	return strings.TrimSpace(newContent)
}

// getMessageTitle 获取消息标题
func (h *pushHandler) getMessageTitle(notificationType string, content interface{}) string {
	switch notificationType {
	case "app_upgrade":
		return "新版本介绍"
	case "train_resource_update":
		return "学习资料更新"
	case "notice":
		if contentMap, ok := content.(map[string]interface{}); ok {
			if text, exists := contentMap["text"]; exists {
				return fmt.Sprintf("%v", text)
			}
		}
	}
	return ""
}

// StorePushNotificationReq 存储推送通知请求
type StorePushNotificationReq struct {
	Slug     string                 `json:"slug" binding:"required"`
	Content  interface{}            `json:"content" binding:"required"`
	Audience map[string]interface{} `json:"audience" binding:"required"` // 推送目标
	Platform []string               `json:"platform"`                    // 推送平台
	URL      string                 `json:"url"`
}
